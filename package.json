{"name": "NotificationApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-firebase/app": "^23.3.1", "@react-native-firebase/messaging": "^23.3.1", "@react-native/new-app-screen": "0.81.4", "@types/react-native-push-notification": "^8.1.4", "react": "19.1.0", "react-native": "0.81.4", "react-native-push-notification": "^8.1.1", "react-native-safe-area-context": "^5.5.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "^20.0.0", "@react-native/babel-preset": "0.81.4", "@react-native/eslint-config": "0.81.4", "@react-native/metro-config": "0.81.4", "@react-native/typescript-config": "0.81.4", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=20"}}