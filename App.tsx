/**
 * React Native Notification App with FCM
 * @format
 */

import React, { useEffect, useState } from 'react';
import {
  StatusBar,
  StyleSheet,
  useColorScheme,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Platform,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import NotificationService from './src/services/NotificationService';
import LocalNotificationService from './src/services/LocalNotificationService';

interface NotificationItem {
  id: string;
  title: string;
  body: string;
  data?: any;
  timestamp: Date;
}

function App() {
  const isDarkMode = useColorScheme() === 'dark';
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [topicName, setTopicName] = useState<string>('general');
  const [permissionStatus, setPermissionStatus] = useState<string>('Unknown');

  useEffect(() => {
    initializeNotifications();
  }, []);

  const initializeNotifications = async () => {
    // Request permissions
    const hasPermission = await NotificationService.requestUserPermission();
    setPermissionStatus(hasPermission ? 'Granted' : 'Denied');

    // Get FCM token
    const token = await NotificationService.getFCMToken();
    setFcmToken(token);

    // Set up notification listeners
    NotificationService.onMessage(
      (message: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('Foreground message received:', message);
        addNotificationToList(message);

        // Show local notification for foreground messages
        LocalNotificationService.showNotification(message);
      },
    );

    NotificationService.onNotificationOpened(
      (message: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('Notification opened:', message);
        addNotificationToList(message);
        Alert.alert(
          'Notification Opened',
          `Title: ${message.notification?.title}\nBody: ${message.notification?.body}`,
        );
      },
    );

    NotificationService.onTokenRefresh((token: string) => {
      console.log('Token refreshed:', token);
      setFcmToken(token);
    });
  };

  const addNotificationToList = (
    message: FirebaseMessagingTypes.RemoteMessage,
  ) => {
    const newNotification: NotificationItem = {
      id: Date.now().toString(),
      title: message.notification?.title || 'No Title',
      body: message.notification?.body || 'No Body',
      data: message.data,
      timestamp: new Date(),
    };

    setNotifications(prev => [newNotification, ...prev]);
  };

  const copyTokenToClipboard = () => {
    if (fcmToken) {
      // In a real app, you'd use Clipboard API
      Alert.alert('FCM Token', fcmToken, [{ text: 'OK' }]);
    }
  };

  const subscribeToTopic = async () => {
    if (topicName.trim()) {
      await NotificationService.subscribeToTopic(topicName.trim());
      Alert.alert('Success', `Subscribed to topic: ${topicName}`);
    }
  };

  const unsubscribeFromTopic = async () => {
    if (topicName.trim()) {
      await NotificationService.unsubscribeFromTopic(topicName.trim());
      Alert.alert('Success', `Unsubscribed from topic: ${topicName}`);
    }
  };

  const sendTestNotification = () => {
    LocalNotificationService.showLocalNotification(
      'Test Notification',
      'This is a test local notification',
      { test: true },
    );
  };

  const clearNotifications = () => {
    setNotifications([]);
    LocalNotificationService.cancelAllNotifications();
  };

  return (
    <SafeAreaProvider>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? '#000' : '#fff' },
        ]}
      >
        <ScrollView
          style={styles.scrollView}
          contentInsetAdjustmentBehavior="automatic"
        >
          <View style={styles.header}>
            <Text
              style={[styles.title, { color: isDarkMode ? '#fff' : '#000' }]}
            >
              FCM Notification App
            </Text>
            <Text
              style={[styles.subtitle, { color: isDarkMode ? '#ccc' : '#666' }]}
            >
              Permission Status: {permissionStatus}
            </Text>
          </View>

          <View style={styles.section}>
            <Text
              style={[
                styles.sectionTitle,
                { color: isDarkMode ? '#fff' : '#000' },
              ]}
            >
              FCM Token
            </Text>
            <TouchableOpacity
              style={styles.tokenContainer}
              onPress={copyTokenToClipboard}
            >
              <Text
                style={[styles.token, { color: isDarkMode ? '#ccc' : '#666' }]}
                numberOfLines={3}
              >
                {fcmToken || 'Loading...'}
              </Text>
            </TouchableOpacity>
            <Text
              style={[styles.hint, { color: isDarkMode ? '#888' : '#999' }]}
            >
              Tap to view full token
            </Text>
          </View>

          <View style={styles.section}>
            <Text
              style={[
                styles.sectionTitle,
                { color: isDarkMode ? '#fff' : '#000' },
              ]}
            >
              Topic Subscription
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: isDarkMode ? '#333' : '#f5f5f5',
                  color: isDarkMode ? '#fff' : '#000',
                },
              ]}
              placeholder="Enter topic name"
              placeholderTextColor={isDarkMode ? '#888' : '#999'}
              value={topicName}
              onChangeText={setTopicName}
            />
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, styles.subscribeButton]}
                onPress={subscribeToTopic}
              >
                <Text style={styles.buttonText}>Subscribe</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.unsubscribeButton]}
                onPress={unsubscribeFromTopic}
              >
                <Text style={styles.buttonText}>Unsubscribe</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text
              style={[
                styles.sectionTitle,
                { color: isDarkMode ? '#fff' : '#000' },
              ]}
            >
              Test Notifications
            </Text>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, styles.testButton]}
                onPress={sendTestNotification}
              >
                <Text style={styles.buttonText}>Send Test</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.clearButton]}
                onPress={clearNotifications}
              >
                <Text style={styles.buttonText}>Clear All</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text
              style={[
                styles.sectionTitle,
                { color: isDarkMode ? '#fff' : '#000' },
              ]}
            >
              Received Notifications ({notifications.length})
            </Text>
            {notifications.length === 0 ? (
              <Text
                style={[
                  styles.emptyText,
                  { color: isDarkMode ? '#888' : '#999' },
                ]}
              >
                No notifications received yet
              </Text>
            ) : (
              notifications.map(notification => (
                <View
                  key={notification.id}
                  style={[
                    styles.notificationItem,
                    {
                      backgroundColor: isDarkMode ? '#333' : '#f9f9f9',
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.notificationTitle,
                      { color: isDarkMode ? '#fff' : '#000' },
                    ]}
                  >
                    {notification.title}
                  </Text>
                  <Text
                    style={[
                      styles.notificationBody,
                      { color: isDarkMode ? '#ccc' : '#666' },
                    ]}
                  >
                    {notification.body}
                  </Text>
                  <Text
                    style={[
                      styles.notificationTime,
                      { color: isDarkMode ? '#888' : '#999' },
                    ]}
                  >
                    {notification.timestamp.toLocaleString()}
                  </Text>
                  {notification.data && (
                    <Text
                      style={[
                        styles.notificationData,
                        { color: isDarkMode ? '#888' : '#999' },
                      ]}
                    >
                      Data: {JSON.stringify(notification.data)}
                    </Text>
                  )}
                </View>
              ))
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  tokenContainer: {
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 6,
    marginBottom: 8,
  },
  token: {
    fontSize: 12,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  hint: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  input: {
    height: 40,
    borderRadius: 6,
    paddingHorizontal: 12,
    marginBottom: 12,
    fontSize: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    height: 44,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  subscribeButton: {
    backgroundColor: '#4CAF50',
  },
  unsubscribeButton: {
    backgroundColor: '#FF5722',
  },
  testButton: {
    backgroundColor: '#2196F3',
  },
  clearButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyText: {
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
  notificationItem: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  notificationBody: {
    fontSize: 14,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
    marginBottom: 4,
  },
  notificationData: {
    fontSize: 11,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
});

export default App;
