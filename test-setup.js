#!/usr/bin/env node

/**
 * Test script to verify the notification app setup
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing React Native FCM Notification App Setup...\n');

// Test 1: Check if required files exist
const requiredFiles = [
  'package.json',
  'App.tsx',
  'src/services/NotificationService.ts',
  'src/services/LocalNotificationService.ts',
  'android/app/google-services.json',
  'ios/NotificationApp/GoogleService-Info.plist',
  'android/app/src/main/AndroidManifest.xml',
  'ios/NotificationApp/AppDelegate.swift',
  'FIREBASE_SETUP.md',
  'README.md'
];

console.log('📁 Checking required files...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

// Test 2: Check package.json dependencies
console.log('\n📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = [
  '@react-native-firebase/app',
  '@react-native-firebase/messaging',
  'react-native-push-notification',
  'react-native-safe-area-context'
];

requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep}`);
  } else {
    console.log(`❌ ${dep} - MISSING`);
  }
});

// Test 3: Check Android configuration
console.log('\n🤖 Checking Android configuration...');

// Check build.gradle for Google Services plugin
const buildGradle = fs.readFileSync('android/build.gradle', 'utf8');
if (buildGradle.includes('com.google.gms:google-services')) {
  console.log('✅ Google Services plugin added to project build.gradle');
} else {
  console.log('❌ Google Services plugin missing from project build.gradle');
}

const appBuildGradle = fs.readFileSync('android/app/build.gradle', 'utf8');
if (appBuildGradle.includes('com.google.gms.google-services')) {
  console.log('✅ Google Services plugin applied in app build.gradle');
} else {
  console.log('❌ Google Services plugin not applied in app build.gradle');
}

// Check AndroidManifest.xml for FCM permissions
const manifest = fs.readFileSync('android/app/src/main/AndroidManifest.xml', 'utf8');
const requiredPermissions = [
  'android.permission.VIBRATE',
  'android.permission.RECEIVE_BOOT_COMPLETED',
  'com.google.android.c2dm.permission.RECEIVE'
];

requiredPermissions.forEach(permission => {
  if (manifest.includes(permission)) {
    console.log(`✅ Permission: ${permission}`);
  } else {
    console.log(`❌ Permission missing: ${permission}`);
  }
});

// Test 4: Check iOS configuration
console.log('\n🍎 Checking iOS configuration...');

const appDelegate = fs.readFileSync('ios/NotificationApp/AppDelegate.swift', 'utf8');
if (appDelegate.includes('import Firebase')) {
  console.log('✅ Firebase imported in AppDelegate');
} else {
  console.log('❌ Firebase not imported in AppDelegate');
}

if (appDelegate.includes('FirebaseApp.configure()')) {
  console.log('✅ Firebase configured in AppDelegate');
} else {
  console.log('❌ Firebase not configured in AppDelegate');
}

if (appDelegate.includes('UNUserNotificationCenterDelegate')) {
  console.log('✅ Notification delegate implemented');
} else {
  console.log('❌ Notification delegate not implemented');
}

// Test 5: Check TypeScript compilation
console.log('\n🔧 Checking TypeScript...');
try {
  const { execSync } = require('child_process');
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.log('❌ TypeScript compilation failed');
  console.log(error.stdout?.toString() || error.message);
}

// Summary
console.log('\n📊 Setup Summary:');
if (missingFiles.length === 0) {
  console.log('✅ All required files are present');
} else {
  console.log(`❌ Missing ${missingFiles.length} required files`);
}

console.log('\n🎯 Next Steps:');
console.log('1. Replace template Firebase configuration files with your actual project files');
console.log('2. Set up Android/iOS development environment (Java, Android Studio, Xcode)');
console.log('3. Run the app: npx react-native run-android or npx react-native run-ios');
console.log('4. Test notifications using Firebase Console');

console.log('\n✨ Setup verification complete!');
