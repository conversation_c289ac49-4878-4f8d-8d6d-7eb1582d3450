require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/react-native/scripts/cocoapods/autolinking'

platform :ios, '16.0'
install! 'cocoapods', :deterministic_uuids => false
use_modular_headers!

target 'NotificationApp' do
  config = use_native_modules!

  # Use Hermes (required for React Native 0.81.4)
  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => true,
    :fabric_enabled => false,
    :new_arch_enabled => false,
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    react_native_post_install(
      installer,
      :mac_catalyst_enabled => false
    )

    # Set deployment target for all pods and disable strict warnings
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
        # Disable warnings as errors to allow build to complete
        config.build_settings['GCC_TREAT_WARNINGS_AS_ERRORS'] = 'NO'
        config.build_settings['CLANG_WARN_DOCUMENTATION_COMMENTS'] = 'NO'
        config.build_settings['GCC_WARN_INHIBIT_ALL_WARNINGS'] = 'NO'
      end
    end
  end
end
