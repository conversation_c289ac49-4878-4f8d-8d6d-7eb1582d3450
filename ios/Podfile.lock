PODS:
  - FBLazyVector (0.81.4)
  - Firebase/CoreOnly (12.2.0):
    - FirebaseCore (~> 12.2.0)
  - Firebase/Messaging (12.2.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 12.2.0)
  - FirebaseCore (12.2.0):
    - FirebaseCoreInternal (~> 12.2.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (12.2.0):
    - FirebaseCore (~> 12.2.0)
  - FirebaseCoreInternal (12.2.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (12.2.0):
    - FirebaseCore (~> 12.2.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (12.2.0):
    - FirebaseCore (~> 12.2.0)
    - FirebaseInstallations (~> 12.2.0)
    - GoogleDataTransport (~> 10.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.81.4):
    - hermes-engine/Pre-built (= 0.81.4)
  - hermes-engine/Pre-built (0.81.4)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - RCTDeprecation (0.81.4)
  - RCTRequired (0.81.4)
  - RCTTypeSafety (0.81.4):
    - FBLazyVector (= 0.81.4)
    - RCTRequired (= 0.81.4)
    - React-Core (= 0.81.4)
  - React (0.81.4):
    - React-Core (= 0.81.4)
    - React-Core/DevSupport (= 0.81.4)
    - React-Core/RCTWebSocket (= 0.81.4)
    - React-RCTActionSheet (= 0.81.4)
    - React-RCTAnimation (= 0.81.4)
    - React-RCTBlob (= 0.81.4)
    - React-RCTImage (= 0.81.4)
    - React-RCTLinking (= 0.81.4)
    - React-RCTNetwork (= 0.81.4)
    - React-RCTSettings (= 0.81.4)
    - React-RCTText (= 0.81.4)
    - React-RCTVibration (= 0.81.4)
  - React-callinvoker (0.81.4)
  - React-Core (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core-prebuilt (0.81.4):
    - ReactNativeDependencies
  - React-Core/CoreModulesHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/Default (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/DevSupport (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.4)
    - React-Core/RCTWebSocket (= 0.81.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTAnimationHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTBlobHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTImageHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTLinkingHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTNetworkHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTSettingsHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTTextHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTVibrationHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTWebSocket (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-CoreModules (0.81.4):
    - RCTTypeSafety (= 0.81.4)
    - React-Core-prebuilt
    - React-Core/CoreModulesHeaders (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.81.4)
    - React-runtimeexecutor
    - ReactCommon
    - ReactNativeDependencies
  - React-cxxreact (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-debug (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - React-timing (= 0.81.4)
    - ReactNativeDependencies
  - React-debug (0.81.4)
  - React-defaultsnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - ReactNativeDependencies
  - React-domnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-Fabric
    - React-Fabric/bridging
    - React-FabricComponents
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-Fabric (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.81.4)
    - React-Fabric/attributedstring (= 0.81.4)
    - React-Fabric/bridging (= 0.81.4)
    - React-Fabric/componentregistry (= 0.81.4)
    - React-Fabric/componentregistrynative (= 0.81.4)
    - React-Fabric/components (= 0.81.4)
    - React-Fabric/consistency (= 0.81.4)
    - React-Fabric/core (= 0.81.4)
    - React-Fabric/dom (= 0.81.4)
    - React-Fabric/imagemanager (= 0.81.4)
    - React-Fabric/leakchecker (= 0.81.4)
    - React-Fabric/mounting (= 0.81.4)
    - React-Fabric/observers (= 0.81.4)
    - React-Fabric/scheduler (= 0.81.4)
    - React-Fabric/telemetry (= 0.81.4)
    - React-Fabric/templateprocessor (= 0.81.4)
    - React-Fabric/uimanager (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/animations (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/attributedstring (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/bridging (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/componentregistry (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/componentregistrynative (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.81.4)
    - React-Fabric/components/root (= 0.81.4)
    - React-Fabric/components/scrollview (= 0.81.4)
    - React-Fabric/components/view (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/legacyviewmanagerinterop (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/root (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/scrollview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/view (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-Fabric/consistency (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/core (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/dom (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/imagemanager (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/leakchecker (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/mounting (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/observers (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/observers/events (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/scheduler (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/telemetry (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/templateprocessor (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/uimanager (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/uimanager/consistency (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-FabricComponents (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.81.4)
    - React-FabricComponents/textlayoutmanager (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.81.4)
    - React-FabricComponents/components/iostextinput (= 0.81.4)
    - React-FabricComponents/components/modal (= 0.81.4)
    - React-FabricComponents/components/rncore (= 0.81.4)
    - React-FabricComponents/components/safeareaview (= 0.81.4)
    - React-FabricComponents/components/scrollview (= 0.81.4)
    - React-FabricComponents/components/switch (= 0.81.4)
    - React-FabricComponents/components/text (= 0.81.4)
    - React-FabricComponents/components/textinput (= 0.81.4)
    - React-FabricComponents/components/unimplementedview (= 0.81.4)
    - React-FabricComponents/components/virtualview (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/iostextinput (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/modal (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/rncore (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/safeareaview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/scrollview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/switch (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/text (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/textinput (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/virtualview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricImage (0.81.4):
    - hermes-engine
    - RCTRequired (= 0.81.4)
    - RCTTypeSafety (= 0.81.4)
    - React-Core-prebuilt
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.81.4)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
    - Yoga
  - React-featureflags (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-featureflagsnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-graphics (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - ReactNativeDependencies
  - React-hermes (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi
    - React-jsiexecutor (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-idlecallbacksnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-ImageManager (0.81.4):
    - React-Core-prebuilt
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - ReactNativeDependencies
  - React-jserrorhandler (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - ReactNativeDependencies
  - React-jsi (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-jsiexecutor (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsinspector (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-oscompat
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsinspectorcdp (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-jsinspectornetwork (0.81.4):
    - React-Core-prebuilt
    - React-featureflags
    - React-jsinspectorcdp
    - React-performancetimeline
    - React-timing
    - ReactNativeDependencies
  - React-jsinspectortracing (0.81.4):
    - React-Core-prebuilt
    - React-oscompat
    - React-timing
    - ReactNativeDependencies
  - React-jsitooling (0.81.4):
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsitracing (0.81.4):
    - React-jsi
  - React-logger (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-Mapbuffer (0.81.4):
    - React-Core-prebuilt
    - React-debug
    - ReactNativeDependencies
  - React-microtasksnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - react-native-safe-area-context (5.6.1):
    - React-Core
  - React-NativeModulesApple (0.81.4):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-featureflags
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-oscompat (0.81.4)
  - React-perflogger (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-performancetimeline (0.81.4):
    - React-Core-prebuilt
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - ReactNativeDependencies
  - React-RCTActionSheet (0.81.4):
    - React-Core/RCTActionSheetHeaders (= 0.81.4)
  - React-RCTAnimation (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTAppDelegate (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTBlob (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTFabric (0.81.4):
    - hermes-engine
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-RCTFBReactNativeSpec (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec/components (= 0.81.4)
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTFBReactNativeSpec/components (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
    - Yoga
  - React-RCTImage (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTLinking (0.81.4):
    - React-Core/RCTLinkingHeaders (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.81.4)
  - React-RCTNetwork (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTRuntime (0.81.4):
    - hermes-engine
    - React-Core
    - React-Core-prebuilt
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - ReactNativeDependencies
  - React-RCTSettings (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTText (0.81.4):
    - React-Core/RCTTextHeaders (= 0.81.4)
    - Yoga
  - React-RCTVibration (0.81.4):
    - React-Core-prebuilt
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-rendererconsistency (0.81.4)
  - React-renderercss (0.81.4):
    - React-debug
    - React-utils
  - React-rendererdebug (0.81.4):
    - React-Core-prebuilt
    - React-debug
    - ReactNativeDependencies
  - React-RuntimeApple (0.81.4):
    - hermes-engine
    - React-callinvoker
    - React-Core-prebuilt
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
  - React-RuntimeCore (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
  - React-runtimeexecutor (0.81.4):
    - React-Core-prebuilt
    - React-debug
    - React-featureflags
    - React-jsi (= 0.81.4)
    - React-utils
    - ReactNativeDependencies
  - React-RuntimeHermes (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-utils
    - ReactNativeDependencies
  - React-runtimescheduler (0.81.4):
    - hermes-engine
    - React-callinvoker
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - ReactNativeDependencies
  - React-timing (0.81.4):
    - React-debug
  - React-utils (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-debug
    - React-jsi (= 0.81.4)
    - ReactNativeDependencies
  - ReactAppDependencyProvider (0.81.4):
    - ReactCodegen
  - ReactCodegen (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - ReactCommon (0.81.4):
    - React-Core-prebuilt
    - ReactCommon/turbomodule (= 0.81.4)
    - ReactNativeDependencies
  - ReactCommon/turbomodule (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - ReactCommon/turbomodule/bridging (= 0.81.4)
    - ReactCommon/turbomodule/core (= 0.81.4)
    - ReactNativeDependencies
  - ReactCommon/turbomodule/bridging (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - ReactNativeDependencies
  - ReactCommon/turbomodule/core (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-debug (= 0.81.4)
    - React-featureflags (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - React-utils (= 0.81.4)
    - ReactNativeDependencies
  - ReactNativeDependencies (0.81.4)
  - RNFBApp (23.3.1):
    - Firebase/CoreOnly (= 12.2.0)
    - React-Core
  - RNFBMessaging (23.3.1):
    - Firebase/Messaging (= 12.2.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - Yoga (0.0.0)

DEPENDENCIES:
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core-prebuilt (from `../node_modules/react-native/React-Core-prebuilt.podspec`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectorcdp (from `../node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)
  - React-jsinspectornetwork (from `../node_modules/react-native/ReactCommon/jsinspector-modern/network`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeDependencies (from `../node_modules/react-native/third-party-podspecs/ReactNativeDependencies.podspec`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-07-07-RNv0.81.0-e0fc67142ec0763c6b6153ca2bf96df815539782
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-Core-prebuilt:
    :podspec: "../node_modules/react-native/React-Core-prebuilt.podspec"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeDependencies:
    :podspec: "../node_modules/react-native/third-party-podspecs/ReactNativeDependencies.podspec"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  FBLazyVector: 9e0cd874afd81d9a4d36679daca991b58b260d42
  Firebase: 26f6f8d460603af3df970ad505b16b15f5e2e9a1
  FirebaseCore: 311c48a147ad4a0ab7febbaed89e8025c67510cd
  FirebaseCoreExtension: 73af080c22a2f7b44cefa391dc08f7e4ee162cb5
  FirebaseCoreInternal: 56ea29f3dad2894f81b060f706f9d53509b6ed3b
  FirebaseInstallations: 3e884b01feabdf67582a80f3250425a00979b4ed
  FirebaseMessaging: 43ec73bbfedd0c385a849bb91593ab4ad4b9e48e
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  hermes-engine: 35c763d57c9832d0eef764316ca1c4d043581394
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCTDeprecation: 7487d6dda857ccd4cb3dd6ecfccdc3170e85dcbc
  RCTRequired: 54128b7df8be566881d48c7234724a78cb9b6157
  RCTTypeSafety: d2b07797a79e45d7b19e1cd2f53c79ab419fe217
  React: 2073376f47c71b7e9a0af7535986a77522ce1049
  React-callinvoker: 00fa0972a70df7408a4f088144b67207b157e386
  React-Core: 7edc3b0763d7a47cf5610b32e0e790ac10dd5410
  React-Core-prebuilt: ab6d77841d4bb8d247c4358663c6c71836e4f461
  React-CoreModules: a02474243c3efbdc767b44dc029b452b0476cee1
  React-cxxreact: b3b6c8c0823ff10237f1c6c1ad067c2577bc2f84
  React-debug: c01d176522cf57cdc4a4a66d1974968fcf497f32
  React-defaultsnativemodule: d8164fe3151fbc98b49580b37bd565da14d9fbfa
  React-domnativemodule: 848a14d603966ec5b244cbd5e4a4c330ae71abb1
  React-Fabric: 027d67e708433a3e08e0257dff8f0293afe1b761
  React-FabricComponents: 378d5c830a42899fb49283d379424f4a6af92265
  React-FabricImage: a844845a53655c23e05e102f29e5c2a5cdfbaed0
  React-featureflags: a190badbbda1e72cf7e7accbfbcd2070ab35c431
  React-featureflagsnativemodule: b0f97eedbbe788c114cf2d0bb5c6b28b55e9b4f9
  React-graphics: eeef269ea0a459baf20748cbbfc099c6263f6596
  React-hermes: 3f94c83c8238913c50fdd23e276d64cfc520b08c
  React-idlecallbacksnativemodule: 7510daba721b4389a2a35fd0a97f5f782bbacbe4
  React-ImageManager: 13f32b59f5c873df59f05b8102ebc075a1d66c4d
  React-jserrorhandler: 9a7140314550bec0368898985ca2f0d87cb0744b
  React-jsi: d987f2b32fdda6c521e750a639ab3b04ab9de44b
  React-jsiexecutor: 63925b9dc840d26880adc1145dc3ea701a2b06a4
  React-jsinspector: 7e36f5182f024ee11b7ac60252db9cd3219deefb
  React-jsinspectorcdp: 7c121234fff135fb6e76534bdbfae1d6e1a05a52
  React-jsinspectornetwork: c0c040786ea67d3b10aa687460d6b96b51365cc5
  React-jsinspectortracing: 73f22a1a52a049409326abb93b8d0d2376a2ed37
  React-jsitooling: f3bb6e50d6d5a1ff581d4ede35722ef760c6b12c
  React-jsitracing: 008ed6c9a92ba5652a23f86cd853248f7fbb9a22
  React-logger: 472e292c035c024ac73070cd6cbd011a827136f9
  React-Mapbuffer: 823a2c0e0d1826c784f808174626d5229f59be17
  React-microtasksnativemodule: 70cdf4826e52c7bcf98bc55255c3e30b5455d318
  react-native-safe-area-context: aac2745e96999c8633d2f6119e4e39b499c2ac8b
  React-NativeModulesApple: e653919faff1f76eadf02373bda26c085bf348e3
  React-oscompat: 73db7dbc80edef36a9d6ed3c6c4e1724ead4236d
  React-perflogger: f2116e7a0c1562ed7cbcaa2c90e8fdb0bc28df78
  React-performancetimeline: 6ea21e31c0ccf56cebe26c7110429fe25314ff22
  React-RCTActionSheet: 9fc2a0901af63cefe09c8df95a08c2cf8bb7797b
  React-RCTAnimation: 3728b2991c073aa629a90df00997b0c6cec54b8e
  React-RCTAppDelegate: 61d352ee16390554f964c13b56eb247b04930a06
  React-RCTBlob: 4ac65a15788a793a42e4eb8c2f4325146aa3baed
  React-RCTFabric: e91c771b3cf221d860d4390cf95c07ae8d9d86ff
  React-RCTFBReactNativeSpec: e4e6b2b177c29f8db880f811fb49a9be88113bfa
  React-RCTImage: 9cc8cff72a6aa2b0a94ad8b0c18b97822825eaee
  React-RCTLinking: fa8aa5a3ed268e364d65f7025f01f94cd9d700a1
  React-RCTNetwork: 139283847dd0dfa98f44c843a49e8dedc5fc2d99
  React-RCTRuntime: b5b5e185f65529a8c61d24632e139993c2d278df
  React-RCTSettings: f101bafea6c0f73bb6cabec765bb7af60eb79e80
  React-RCTText: b5a219996342e6727dae7f07e6ddcc5377fc32a2
  React-RCTVibration: 3a04d19e46e19af9cdfd43b9bb17fa244c633100
  React-rendererconsistency: b83b300e607f4e30478a5c3365e260a760232b04
  React-renderercss: 10faf35f1b235ee768d84bca40733418669dd8bb
  React-rendererdebug: af3c8224c58498efdc0c10c3c376b724f8896668
  React-RuntimeApple: 119f51db881637ffcc6186c73f6a40840fd3bc0e
  React-RuntimeCore: 2394c9276eb30c7156e9eb93091693ed6864b9a8
  React-runtimeexecutor: 3b2bed6b77b4016273d354bcb1d6eb2ad5a457a2
  React-RuntimeHermes: 3148d449c52f1ff987eebbb72ce2ed7542e31b5f
  React-runtimescheduler: ef67578dc2c5b27ec32b322591d965cef6e8e8a4
  React-timing: 37d287f84c57ce9186fbb494dbbdb3a272a3ee19
  React-utils: 3c6c77bb22ce2e5947daf072d49f361a95fd12c6
  ReactAppDependencyProvider: b20fba6c3d091a393925890009999472c8f94d95
  ReactCodegen: c7382a9d84b5826fec1441c7eec6e424499bc8c0
  ReactCommon: b720ccad5e1e8a528746c39b671825fcb7207d3c
  ReactNativeDependencies: ed6d1e64802b150399f04f1d5728ec16b437251e
  RNFBApp: a1b5a014860207f60c7f4826e8ebad91939c07c7
  RNFBMessaging: 137eaef10a2f948c02d68a47e683a66dede82682
  Yoga: c8f1aad5bc3522a8fdab409b5843965abff35aa6

PODFILE CHECKSUM: d0287807d173bf089c88ec4086c8bf1a1b0d9171

COCOAPODS: 1.15.2
