# React Native FCM Notification App

A comprehensive React Native application demonstrating Firebase Cloud Messaging (FCM) integration with the latest technologies.

## Features

- 🔔 **Push Notifications**: Receive notifications in foreground, background, and quit states
- 🎯 **Topic Subscriptions**: Subscribe/unsubscribe to notification topics
- 📱 **Cross-Platform**: Works on both iOS and Android
- 🌙 **Dark Mode Support**: Automatic dark/light theme switching
- 📊 **Notification History**: View received notifications with timestamps
- 🧪 **Test Notifications**: Send local test notifications
- 🔑 **FCM Token Management**: View and manage FCM tokens
- ⚡ **Latest Technologies**: React Native 0.81.4, Firebase v12, TypeScript

## Prerequisites

- Node.js (v16 or higher)
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development)
- Firebase project with FCM enabled

## Installation

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **iOS Setup:**

   ```bash
   cd ios && bundle exec pod install
   ```

3. **Firebase Configuration:**
   - Follow the instructions in `FIREBASE_SETUP.md`
   - Replace template configuration files with your Firebase project files

## Firebase Setup

### Android Configuration

1. Add your `google-services.json` to `android/app/`
2. Ensure package name matches: `com.notificationapp`

### iOS Configuration

1. Add your `GoogleService-Info.plist` to `ios/NotificationApp/`
2. Ensure bundle ID matches: `org.reactjs.native.example.NotificationApp`
3. Enable Push Notifications capability in Xcode

## Running the App

### Android

```bash
npx react-native run-android
```

### iOS

```bash
npx react-native run-ios
```

## App Features

### 1. Permission Management

- Automatic permission request on app launch
- Permission status display
- Handles iOS and Android 13+ notification permissions

### 2. FCM Token

- Displays current FCM token
- Token refresh handling
- Tap to view full token

### 3. Topic Subscriptions

- Subscribe to notification topics
- Unsubscribe from topics
- Default topic: "general"

### 4. Notification Handling

- **Foreground**: Shows local notification + adds to history
- **Background**: Opens app when tapped + adds to history
- **Quit State**: Opens app when tapped + adds to history

### 5. Test Features

- Send local test notifications
- Clear notification history
- View notification data and timestamps

## Testing Notifications

### 1. Using Firebase Console

1. Go to Firebase Console > Cloud Messaging
2. Click "Send your first message"
3. Use the FCM token from the app
4. Send test notifications

### 2. Using FCM REST API

```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "FCM_TOKEN_FROM_APP",
    "notification": {
      "title": "Test Notification",
      "body": "This is a test message"
    },
    "data": {
      "custom_key": "custom_value"
    }
  }'
```

### 3. Topic Notifications

```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "/topics/general",
    "notification": {
      "title": "Topic Notification",
      "body": "Message for all subscribers"
    }
  }'
```

## Project Structure

```
NotificationApp/
├── src/
│   └── services/
│       ├── NotificationService.ts      # FCM service
│       └── LocalNotificationService.ts # Local notifications
├── android/
│   └── app/
│       ├── google-services.json        # Firebase config
│       └── src/main/
│           ├── AndroidManifest.xml     # Permissions & services
│           └── res/                    # Resources
├── ios/
│   └── NotificationApp/
│       ├── GoogleService-Info.plist    # Firebase config
│       └── AppDelegate.swift           # iOS notification setup
├── App.tsx                             # Main app component
├── FIREBASE_SETUP.md                   # Firebase setup guide
└── README.md                           # This file
```

## Key Dependencies

- `@react-native-firebase/app`: Firebase core
- `@react-native-firebase/messaging`: FCM messaging
- `react-native-push-notification`: Local notifications
- `react-native-safe-area-context`: Safe area handling

## Troubleshooting

### Common Issues

1. **No FCM Token**

   - Check Firebase configuration files
   - Verify app bundle ID/package name matches Firebase project
   - Ensure internet connection

2. **Notifications Not Received**

   - Check notification permissions
   - Verify Firebase project has FCM enabled
   - Test with Firebase Console first

3. **iOS Build Issues**

   - Run `cd ios && bundle exec pod install`
   - Clean build folder in Xcode
   - Check provisioning profiles

4. **Android Build Issues**
   - Clean project: `cd android && ./gradlew clean`
   - Check Google Services plugin is applied
   - Verify google-services.json is in correct location

### Debug Tips

- Check React Native logs: `npx react-native log-android` or `npx react-native log-ios`
- Monitor Firebase Console for delivery reports
- Use device logs for native debugging

## Production Considerations

- Replace template Firebase configuration with production keys
- Implement proper error handling and retry logic
- Add analytics for notification engagement
- Consider notification scheduling and batching
- Implement deep linking for notification actions
- Add notification categories and actions
- Handle notification badges and sounds appropriately

## License

MIT License - feel free to use this project as a starting point for your own notification-enabled React Native apps.
