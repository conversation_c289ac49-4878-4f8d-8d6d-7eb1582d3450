# Firebase Setup Guide

## Prerequisites
1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Firebase Cloud Messaging (FCM) in your project

## Android Setup
1. In Firebase Console, add an Android app to your project
2. Use package name: `com.notificationapp`
3. Download the `google-services.json` file
4. Replace the template file at `android/app/google-services.json` with your downloaded file

## iOS Setup
1. In Firebase Console, add an iOS app to your project
2. Use bundle ID: `org.reactjs.native.example.NotificationApp`
3. Download the `GoogleService-Info.plist` file
4. Replace the template file at `ios/NotificationApp/GoogleService-Info.plist` with your downloaded file
5. In Xcode, add the `GoogleService-Info.plist` file to your project (drag and drop into the project navigator)

## Important Notes
- The current configuration files are templates with dummy data
- You MUST replace them with your actual Firebase project configuration files
- Without proper configuration, push notifications will not work
- Make sure to enable FCM in your Firebase project settings

## Testing
After setup, you can test notifications using:
1. Firebase Console > Cloud Messaging > Send your first message
2. Use the FCM token logged in the app console
3. Send test notifications to verify the setup is working

## Security
- Never commit real Firebase configuration files to public repositories
- Consider using environment variables for sensitive configuration in production
