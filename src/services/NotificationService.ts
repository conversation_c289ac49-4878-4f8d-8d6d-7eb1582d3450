import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { Platform, PermissionsAndroid, Alert } from 'react-native';

export interface NotificationData {
  title?: string;
  body?: string;
  data?: { [key: string]: string };
}

class NotificationService {
  private fcmToken: string | null = null;
  private onTokenRefreshCallback?: (token: string) => void;
  private onMessageCallback?: (message: FirebaseMessagingTypes.RemoteMessage) => void;
  private onNotificationOpenedCallback?: (message: FirebaseMessagingTypes.RemoteMessage) => void;

  constructor() {
    this.initializeMessaging();
  }

  private async initializeMessaging() {
    // Request permission for iOS and Android 13+
    await this.requestUserPermission();

    // Get the FCM token
    await this.getFCMToken();

    // Listen for token refresh
    messaging().onTokenRefresh(token => {
      console.log('FCM Token refreshed:', token);
      this.fcmToken = token;
      if (this.onTokenRefreshCallback) {
        this.onTokenRefreshCallback(token);
      }
    });

    // Listen for foreground messages
    messaging().onMessage(async remoteMessage => {
      console.log('Foreground message received:', remoteMessage);
      if (this.onMessageCallback) {
        this.onMessageCallback(remoteMessage);
      }
    });

    // Listen for background/quit state messages
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('Notification caused app to open from background state:', remoteMessage);
      if (this.onNotificationOpenedCallback) {
        this.onNotificationOpenedCallback(remoteMessage);
      }
    });

    // Check if app was opened from a quit state
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('Notification caused app to open from quit state:', remoteMessage);
          if (this.onNotificationOpenedCallback) {
            this.onNotificationOpenedCallback(remoteMessage);
          }
        }
      });
  }

  async requestUserPermission(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('Authorization status:', authStatus);
        return true;
      } else {
        console.log('Permission denied');
        return false;
      }
    } else if (Platform.OS === 'android') {
      if (Platform.Version >= 33) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
      return true; // For Android < 13, permission is granted by default
    }
    return false;
  }

  async getFCMToken(): Promise<string | null> {
    try {
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
      this.fcmToken = token;
      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  getStoredToken(): string | null {
    return this.fcmToken;
  }

  onTokenRefresh(callback: (token: string) => void) {
    this.onTokenRefreshCallback = callback;
  }

  onMessage(callback: (message: FirebaseMessagingTypes.RemoteMessage) => void) {
    this.onMessageCallback = callback;
  }

  onNotificationOpened(callback: (message: FirebaseMessagingTypes.RemoteMessage) => void) {
    this.onNotificationOpenedCallback = callback;
  }

  async subscribeToTopic(topic: string): Promise<void> {
    try {
      await messaging().subscribeToTopic(topic);
      console.log(`Subscribed to topic: ${topic}`);
    } catch (error) {
      console.error(`Error subscribing to topic ${topic}:`, error);
    }
  }

  async unsubscribeFromTopic(topic: string): Promise<void> {
    try {
      await messaging().unsubscribeFromTopic(topic);
      console.log(`Unsubscribed from topic: ${topic}`);
    } catch (error) {
      console.error(`Error unsubscribing from topic ${topic}:`, error);
    }
  }

  showAlert(title: string, message: string) {
    Alert.alert(title, message, [{ text: 'OK' }]);
  }

  async deleteToken(): Promise<void> {
    try {
      await messaging().deleteToken();
      this.fcmToken = null;
      console.log('FCM token deleted');
    } catch (error) {
      console.error('Error deleting FCM token:', error);
    }
  }
}

export default new NotificationService();
