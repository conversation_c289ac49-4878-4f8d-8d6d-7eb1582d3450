import { Platform } from 'react-native';
import PushNotification, { Importance } from 'react-native-push-notification';
import { FirebaseMessagingTypes } from '@react-native-firebase/messaging';

class LocalNotificationService {
  constructor() {
    this.configure();
    this.createDefaultChannel();
  }

  configure() {
    PushNotification.configure({
      onRegister: function (token) {
        console.log('Local notification token:', token);
      },

      onNotification: function (notification) {
        console.log('Local notification:', notification);
      },

      onAction: function (notification) {
        console.log('Local notification action:', notification.action);
        console.log('Local notification:', notification);
      },

      onRegistrationError: function (err) {
        console.error('Local notification registration error:', err.message, err);
      },

      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },

      popInitialNotification: true,
      requestPermissions: Platform.OS === 'ios',
    });
  }

  createDefaultChannel() {
    PushNotification.createChannel(
      {
        channelId: 'fcm_default_channel',
        channelName: 'Default',
        channelDescription: 'A default channel for notifications',
        playSound: true,
        soundName: 'default',
        importance: Importance.HIGH,
        vibrate: true,
      },
      (created) => console.log(`createChannel returned '${created}'`)
    );
  }

  showNotification(remoteMessage: FirebaseMessagingTypes.RemoteMessage) {
    const { notification, data } = remoteMessage;
    
    PushNotification.localNotification({
      title: notification?.title || 'New Notification',
      message: notification?.body || 'You have a new message',
      playSound: true,
      soundName: 'default',
      channelId: 'fcm_default_channel',
      userInfo: data,
      actions: ['View'],
      invokeApp: true,
    });
  }

  showLocalNotification(title: string, message: string, data?: any) {
    PushNotification.localNotification({
      title,
      message,
      playSound: true,
      soundName: 'default',
      channelId: 'fcm_default_channel',
      userInfo: data,
      actions: ['View'],
      invokeApp: true,
    });
  }

  cancelAllNotifications() {
    PushNotification.cancelAllLocalNotifications();
  }

  cancelNotification(id: string) {
    PushNotification.cancelLocalNotifications({ id });
  }

  getScheduledNotifications(callback: (notifications: any[]) => void) {
    PushNotification.getScheduledLocalNotifications(callback);
  }

  scheduleNotification(title: string, message: string, date: Date, data?: any) {
    PushNotification.localNotificationSchedule({
      title,
      message,
      date,
      playSound: true,
      soundName: 'default',
      channelId: 'fcm_default_channel',
      userInfo: data,
      actions: ['View'],
      invokeApp: true,
    });
  }
}

export default new LocalNotificationService();
